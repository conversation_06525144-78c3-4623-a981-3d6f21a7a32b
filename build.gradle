plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.3'
	id 'io.spring.dependency-management' version '1.1.7'
	id 'com.diffplug.spotless' version '7.0.3'
	id 'jacoco'
	id 'groovy'
}

group = 'com.decurret_dcp.dcjpy'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

springBoot {
	mainClass = 'com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication'
}

repositories {
	mavenCentral()
}
// Ensure exclude logging default
configurations.configureEach { exclude group: "org.springframework.boot", module: "spring-boot-starter-logging" }

dependencies {
	// Spring Boot starter (Web application)
	implementation ('org.springframework.boot:spring-boot-starter') {
        //Exclude for using log4j2 instead of logging default
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
	// Spring Context
	implementation 'org.springframework:spring-context'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	// Spring Boot Starter Web
	implementation 'org.springframework.boot:spring-boot-starter-web'

	// AWS SDK for Java
	implementation platform('software.amazon.awssdk:bom:2.31.50')
	implementation 'software.amazon.awssdk:s3'
	implementation 'software.amazon.awssdk:dynamodb'
	implementation 'software.amazon.awssdk:sqs'

	// Spring Retry
	implementation 'org.springframework.retry:spring-retry:2.0.12'
	implementation 'org.springframework:spring-aspects:7.0.0-M5'

	// Web3j (Ethereum client)
	implementation 'org.web3j:core:4.14.0'

    // Lof4j2
    implementation("org.springframework.boot:spring-boot-starter-log4j2")

	// Jackson (JSON processing)
	implementation 'com.fasterxml.jackson.core:jackson-databind:2.18.1'

	// UUID
	implementation 'com.github.f4b6a3:uuid-creator:6.1.1'

	// Groovy
	testImplementation 'org.apache.groovy:groovy:4.0.27'

	// Spock
	implementation 'org.apache.groovy:groovy-all:4.0.27'
	testImplementation 'org.spockframework:spock-core:2.4-M6-groovy-4.0'
	testImplementation 'org.spockframework:spock-spring:2.4-M6-groovy-4.0'
	testImplementation platform('org.spockframework:spock-bom:2.4-M6-groovy-4.0')

	// TestContainers for managing Docker containers in tests
	testImplementation 'org.testcontainers:testcontainers:1.17.6'
	testImplementation 'org.testcontainers:spock:1.17.6'

	// Spring Boot Test
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher:1.11.3'
	testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.11.3'

	// Byte Buddy Agent for Mockito
	testImplementation 'net.bytebuddy:byte-buddy-agent:1.17.6'

	// Byte Buddy for Generate Dynamic Class in Runtime
	implementation("net.bytebuddy:byte-buddy:1.17.6")
}

spotless {
	java {
		googleJavaFormat('1.23.0')
		importOrder()
		removeUnusedImports()
		formatAnnotations()
		trimTrailingWhitespace()
		endWithNewline()
	}
	groovyGradle {
		trimTrailingWhitespace()
		endWithNewline()
	}
	groovy {
		target 'src/test/groovy/**/*.groovy'
		importOrder()
		removeSemicolons()
		trimTrailingWhitespace()
		endWithNewline()
	}
}

tasks.withType(GroovyCompile) {
	groovyOptions.forkOptions.jvmArgs = [
		'--add-opens=java.base/java.lang=ALL-UNNAMED',
		'--add-opens=java.base/java.util=ALL-UNNAMED'
	]
	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}

tasks.named('test') {
	useJUnitPlatform()
	testLogging {
		events "STARTED", "PASSED", "FAILED", "SKIPPED"
		displayGranularity = 4
	}
	jvmArgs "-javaagent:${classpath.find { it.name.contains('byte-buddy-agent') }}"
	exclude "adhoc/**"
}

jacoco {
	toolVersion = '0.8.12'
}

jacocoTestReport {
	reports {
		xml.required = true
		csv.required = false
	}

	classDirectories.setFrom(
			files(classDirectories.files.collect {
				fileTree(dir: it, include: [
					'com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/ethereum/**'
					,
					'com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/s3/**'
					,
					'com/decurret_dcp/dcjpy/bcmonitoring/adaptor/infrastructure/dynamodb/**'
					,
					'com/decurret_dcp/dcjpy/bcmonitoring/application/**'
					,
					'com/decurret_dcp/dcjpy/bcmonitoring/usecase/**'
				])
			})
			)
}

jacocoTestCoverageVerification {
	violationRules {
		rule {
			limit {
				minimum = 0.8
			}
		}
	}
}

test.finalizedBy jacocoTestReport

tasks.register('testAdhoc', Test) {
	dependsOn testClasses, bootJar
	useJUnitPlatform()
	testLogging {
		events "STARTED", "PASSED", "FAILED", "SKIPPED"
		displayGranularity = 4
		// For debugging: change showStandardStreams = true to view test's logs
		showStandardStreams = false
	}
	jvmArgs "-javaagent:${classpath.find { it.name.contains('byte-buddy-agent') }}"
	exclude "com/**"
}
testAdhoc.finalizedBy jacocoTestReport
