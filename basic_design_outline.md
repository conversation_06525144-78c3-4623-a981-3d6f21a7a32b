# 設計書の基本方針
ロジックレベルの詳細な設計書は、重複メンテナンスやソースコードとの乖離を招くリスクがある。そのため、より抽象化された汎用的な概要資料として位置づけ、長期的に価値を保てる内容を目指している
具体的な実装詳細よりも、システムの全体像、設計思想、重要な判断基準などを重視

# 各種基本設計書のアウトライン

## a. BCMonitoringアーキテクチャ概要

BCMonitoringアーキテクチャ概要には、以下の要素を記述する。

1. BCMonitoringを中心とした周辺のAWSアーキテクチャ概要図
2. BCMonitoringを実装するうえでの言語仕様、必要ライブラリなどのコンポーネント概要
3. パフォーマンス・スケーリング
   * BCMonitoringをスケーリングさせるうえでの前提条件（→水平スケールできる仕組みでないことをまとめておく必要がある）
4. Listenerノード接続管理
   * Listenerノードとの接続方式と必要接続数

## b. BCMonitoring実装方針

1. アプリケーションのメイン処理概要（sequenced diagram）：
   1. 処理概要
   2. 処理フロー（シーケンス図）

2. 非同期処理の概要設計書
   1. 処理概要
   2. 非同期処理が必要な理由
      1. イベント監視の連続性保証：
         1. pending transaction処理中も新規ブロック監視を継続する必要性
         2. ブロックチェーンは止まらない → 監視も止められない
   3. 処理フロー（非同期処理がわかるシーケンス図）

3. Web3j統合の設計書
   1. Web3jの概要
   2. 利用しているWeb3jメソッド一覧
   3. Web3jライブラリの使用方針
      1. リアルタイム監視: 新規ブロック通知の即座受信
         1. 概要
         2. 処理フロー（シーケンス図）
      2. データ取得: ブロック詳細・トランザクション情報の取得
         1. 概要
         2. 処理フロー（シーケンス図）
      3. イベント解析: スマートコントラクトイベントログの解析
         1. 概要
         2. 処理フロー（シーケンス図）
   4. 接続アーキテクチャ
      1. 接続方式の使い分け
      2. 接続管理設計
   5. ABI統合とイベント処理
      1. ABI統合アーキテクチャ
      2. イベントフィルタリング
         1. ABI定義による動的フィルタリング
            1. Web3jのエンコードメソッド（EventEncoder.encode()） によるシグネチャ生成
            2. 登録済みイベントのみ処理対象
            3. 未知イベントの除外
      3. イベントデコーディング
   6. エラーハンドリング
      * WebSocket切断
         * 自動再接続設定
      * HTTP RPC エラー
      * データレベルエラー

## c. テーブル設計

## d. BCMonitoring SpringBootプロジェクト構成

### 1. BCMonitoring 概要

BCMonitoringは、Ethereumブロックチェーンから発生するイベントをリアルタイムで監視し、そのデータをAWSサービス（DynamoDB、S3、SQS）に保存するJava 21 Spring Bootアプリケーションです。

#### 主要機能
- **リアルタイムブロック監視**: Web3j WebSocketを使用した新規ブロック通知の購読
- **イベントログ解析**: ABI定義に基づくスマートコントラクトイベントの解析・フィルタリング
- **データ永続化**: DynamoDBへのイベントデータとブロック高の保存
- **ABI管理**: S3からのスマートコントラクトABI定義の動的取得・パース
- **エラーハンドリング**: 包括的なエラー処理とリトライ機能

#### 技術スタック
- **Java 21**: 最新のJava機能（Virtual Threads等）を活用
- **Spring Boot 3.4.3**: 依存性注入、設定管理、Web機能
- **Web3j 4.14.0**: Ethereumブロックチェーンとの統合
- **AWS SDK**: DynamoDB、S3、SQS との連携
- **Lombok**: ボイラープレートコードの削減
- **Logback + Logstash**: 構造化ログ出力

### 2. リポジトリ構成方針

#### 2.1 Clean Architecture採用理由

BCMonitoringプロジェクトでは、以下の理由によりClean Architectureを採用しています：

**1. 関心の分離**
- ビジネスロジック（Domain）と外部システム（Infrastructure）の明確な分離
- 各レイヤーの責務を明確化し、保守性を向上

**2. テスタビリティの向上**
- 依存関係の逆転により、モックを使用した単体テストが容易
- インフラストラクチャ層の変更がビジネスロジックに影響しない

**3. 技術的負債の軽減**
- 外部ライブラリやフレームワークの変更が内部ロジックに与える影響を最小化
- 段階的なリファクタリングが可能

**4. 拡張性の確保**
- 新しい外部システムとの統合が容易
- 機能追加時の影響範囲を限定

#### 2.2 レイヤー分離の方針

```
┌─────────────────────────────────────────┐
│              Controller                 │ ← REST API、診断エンドポイント
├─────────────────────────────────────────┤
│             Application                 │ ← ビジネスロジック、ユースケース
├─────────────────────────────────────────┤
│               Domain                    │ ← エンティティ、ドメインモデル
├─────────────────────────────────────────┤
│            Infrastructure               │ ← 外部システム統合
│  ┌─────────┬─────────┬─────────────────┐ │
│  │DynamoDB │   S3    │    Ethereum     │ │
│  └─────────┴─────────┴─────────────────┘ │
└─────────────────────────────────────────┘
```

**依存関係の方向性**
- Controller → Application → Domain
- Infrastructure → Domain（依存関係の逆転）
- 各レイヤーは下位レイヤーのみに依存

### 3. 各ディレクトリの役割

#### 3.1 パッケージ構成

```
com.decurret_dcp.dcjpy.bcmonitoring/
├── BcmonitoringApplication.java          # Spring Boot メインクラス
├── controller/                           # プレゼンテーション層
│   └── DiagnosticController.java         # ヘルスチェック、診断API
├── application/                          # アプリケーション層
│   ├── event/
│   │   └── MonitorEventService.java      # イベント監視制御
│   └── abi/
│       └── DownloadAbiService.java       # ABI取得・パース制御
├── domain/                               # ドメイン層
│   └── model/
│       ├── Event.java                    # イベントエンティティ
│       ├── BlockHeight.java              # ブロック高エンティティ
│       └── Transaction.java              # トランザクションエンティティ
├── adaptor/                              # インフラストラクチャ層
│   └── infrastructure/
│       ├── ethereum/                     # Ethereum統合
│       │   ├── EthEventLogDao.java       # Web3j WebSocket管理
│       │   ├── EventLogRepository.java   # インターフェース
│       │   └── EventLogRepositoryImpl.java # 実装
│       ├── dynamodb/                     # DynamoDB統合
│       │   ├── EventDao.java             # イベントデータアクセス
│       │   ├── EventRepository.java      # インターフェース
│       │   ├── BlockHeightDao.java       # ブロック高データアクセス
│       │   └── BlockHeightRepository.java # インターフェース
│       ├── s3/                           # S3統合
│       │   ├── AbiParser.java            # ABI解析
│       │   ├── S3AbiRepository.java      # S3アクセス
│       │   └── S3ClientAdaptor.java      # S3クライアント
│       └── sqs/                          # SQS統合（将来拡張用）
├── config/                               # 設定管理
│   ├── BcmonitoringConfigurationProperties.java # 中央設定
│   ├── Web3jConfig.java                  # Web3j設定
│   ├── MonitoringRunnerConfig.java       # 監視実行設定
│   ├── AwsCredentialsConfig.java         # AWS認証設定
│   ├── S3Config.java                     # S3設定
│   ├── DynamoDBConnectionPool.java       # DynamoDB接続プール
│   ├── ThreadConfig.java                 # スレッド設定
│   ├── JacksonConfig.java                # JSON設定
│   ├── LoggingConfig.java                # ログ設定
│   └── ContextConfig.java                # アプリケーション状態管理
├── exception/                            # 例外処理
│   ├── BcmonitoringException.java        # 基底例外
│   ├── BlockchainException.java          # ブロックチェーン例外
│   ├── ConfigurationException.java       # 設定例外
│   ├── DataAccessException.java          # データアクセス例外
│   ├── S3Exception.java                  # S3例外
│   ├── ResourceNotFoundException.java    # リソース未発見例外
│   ├── UnsupportedTypeException.java     # 未サポート型例外
│   ├── ErrorResponse.java                # エラーレスポンス
│   └── GlobalExceptionHandler.java       # グローバル例外ハンドラ
├── logging/                              # ログ管理
│   ├── LoggingService.java               # ログサービス
│   └── StructuredLogContext.java         # 構造化ログコンテキスト
└── consts/                               # 定数定義
    └── DCFConst.java                     # アプリケーション定数
```

#### 3.2 各レイヤーの詳細

**Controller層（プレゼンテーション層）**
- 責務: HTTP リクエスト/レスポンスの処理、入力検証
- 主要クラス:
  - `DiagnosticController`: ヘルスチェック、システム診断API

**Application層（アプリケーション層）**
- 責務: ビジネスロジックの調整、ユースケースの実装
- 主要クラス:
  - `MonitorEventService`: イベント監視の制御・調整
  - `DownloadAbiService`: ABI取得・パース処理の制御

**Domain層（ドメイン層）**
- 責務: ビジネスエンティティ、ドメインモデルの定義
- 主要クラス:
  - `Event`: イベントデータのドメインモデル
  - `BlockHeight`: ブロック高のドメインモデル
  - `Transaction`: トランザクションのドメインモデル

**Infrastructure層（インフラストラクチャ層）**
- 責務: 外部システムとの統合、データ永続化
- サブパッケージ:
  - `ethereum`: Web3j を使用したEthereum統合
  - `dynamodb`: AWS DynamoDB との統合
  - `s3`: AWS S3 との統合
  - `sqs`: AWS SQS との統合（将来拡張用）

**Config層（設定層）**
- 責務: アプリケーション設定、Bean定義、外部システム接続設定
- 環境別設定ファイルによる設定管理

**Exception層（例外処理層）**
- 責務: アプリケーション全体の例外処理、エラーハンドリング
- 階層化された例外クラス構成

**Logging層（ログ管理層）**
- 責務: 構造化ログ出力、ログコンテキスト管理
- JSON形式での構造化ログ出力

#### 3.3 データフロー

```mermaid
graph TD
    A[Ethereum Network] -->|WebSocket| B[EthEventLogDao]
    B --> C[MonitorEventService]
    C --> D[EventRepository]
    D --> E[DynamoDB]

    F[S3] -->|ABI Files| G[AbiParser]
    G --> H[DownloadAbiService]
    H --> C

    I[DiagnosticController] --> J[Health Check Response]
```

1. **起動時**: S3からABI定義を取得・パース
2. **監視開始**: Web3j WebSocketでEthereumブロック監視開始
3. **イベント処理**: 新規ブロック受信 → イベント解析 → DynamoDB保存
4. **診断**: REST APIによるシステム状態確認

この構成により、各レイヤーの責務が明確に分離され、保守性・テスタビリティ・拡張性を確保したアーキテクチャを実現しています。

## e. アプリケーション基本ログ指針

1. BCMonitoring関連アプリケーション基本ログ指針
   1. ログの使用方針
   2. 監視・アラート設計
      1. ブロック遅延のWARNが5回継続出る場合はアラートを流すなどアラートのパターン設計する
   3. ログ禁止事項
      1. 秘匿情報（秘密鍵等）に該当するものはログ出力しない

2. BCMonitoringのログ設計
   1. ログ出力先
      1. New Relic, Cloud Watch, Slackなどへ出力する
   2. ログフォーマット
   3. ログ出力項目（どのアクションがどのログに該当するか、ログレベルごとの出力情報定義）
      1. 使用ライブラリ
      2. fatalログ
      3. errorログ
      4. warningログ
      5. infoログ
      6. debugログ