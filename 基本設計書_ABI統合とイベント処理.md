# 基本設計書_ABI統合とイベント処理

## 1. 概要

### 機能の目的と責務
- **目的**: Ethereumスマートコントラクトから発生するイベントを動的に解析・フィルタリングし、構造化データとして処理する
- **責務**: 
  - S3からのABI定義ファイルの取得・パース
  - ABI定義に基づく動的イベントフィルタリング
  - Web3jを使用したイベントシグネチャ生成
  - イベントログの構造化データ変換
  - 複数コントラクトの並行処理

### 他機能との関連性
- **前提機能**: 
  - S3接続管理（S3Config、S3ClientAdaptor）
  - Web3j統合（Web3jConfig、EthEventLogDao）
- **後続機能**: 
  - 新規ブロック処理機能（フィルタリングされたイベントの処理）
  - DynamoDB保存機能（構造化データの永続化）
- **並行機能**: 
  - ブロックチェーン監視機能（リアルタイムイベント検知）

### 前提条件・制約事項
- **前提条件**:
  - S3バケットにABI JSONファイルが配置されていること
  - ABI JSONファイルがHardhat/Truffle形式に準拠していること
  - Web3j接続が確立されていること
- **制約事項**:
  - ABI定義の動的更新は再起動が必要
  - メモリ使用量はABIファイル数に比例
  - イベントシグネチャの重複は後勝ち

## 2. ABI統合アーキテクチャ

### 2.1 アーキテクチャ概要

```mermaid
graph TD
    A[S3 Bucket] -->|ABI JSON Files| B[S3AbiRepository]
    B --> C[DownloadAbiService]
    C --> D[AbiParser]
    D --> E[StructGenerator]
    E --> F[Dynamic Event Classes]
    
    G[Ethereum Events] --> H[EthEventLogDao]
    H --> I[Event Filtering]
    I --> F
    F --> J[Structured Event Data]
    J --> K[DynamoDB]
    
    L[Web3j EventEncoder] --> M[Event Signature Generation]
    M --> I
```

### 2.2 主要コンポーネント

#### 2.2.1 S3AbiRepository
- **責務**: S3からのABI JSONファイル取得
- **主要メソッド**:
  - `listObjects(bucketName, prefix)`: ABI ファイル一覧取得
  - `getObject(bucketName, key)`: 個別ABI ファイル取得

#### 2.2.2 DownloadAbiService
- **責務**: ABI取得プロセスの制御・調整
- **主要メソッド**:
  - `execute()`: ABI取得・パース処理の実行
  - **処理フロー**:
    1. S3バケット設定確認
    2. ABI ファイル一覧取得
    3. 各ファイルの並行取得・パース

#### 2.2.3 AbiParser
- **責務**: ABI JSON解析・動的クラス生成
- **主要メソッド**:
  - `parseAbiContent(inputStream, objKey, lastModified)`: ABI解析
  - `generateEventClasses()`: 動的イベントクラス生成

#### 2.2.4 StructGenerator
- **責務**: ByteBuddyを使用した動的クラス生成
- **機能**:
  - ABI定義からJavaクラス動的生成
  - Web3j TypedContractとの統合
  - イベントパラメータの型変換

### 2.3 ABI統合シーケンス

```mermaid
sequenceDiagram
    participant App as Application
    participant DAS as DownloadAbiService
    participant S3R as S3AbiRepository
    participant AP as AbiParser
    participant SG as StructGenerator
    participant S3 as S3 Bucket

    App->>DAS: execute()
    DAS->>S3R: listObjects(bucketName, prefix)
    S3R->>S3: List ABI files
    S3-->>S3R: File list
    S3R-->>DAS: ABI file objects

    loop For each ABI file
        DAS->>S3R: getObject(bucketName, objKey)
        S3R->>S3: Get ABI content
        S3-->>S3R: ABI JSON
        S3R-->>DAS: InputStream
        
        DAS->>AP: parseAbiContent(inputStream, objKey, lastModified)
        AP->>AP: Parse JSON structure
        AP->>SG: generateEventClasses(abiDefinition)
        SG->>SG: Create dynamic classes with ByteBuddy
        SG-->>AP: Generated event classes
        AP-->>DAS: Parsing complete
    end
    
    DAS-->>App: ABI integration complete
```

## 3. イベントフィルタリング

### 3.1 ABI定義による動的フィルタリング

#### 3.1.1 フィルタリング戦略

**1. シグネチャベースフィルタリング**
- Web3j EventEncoder.encode()によるイベントシグネチャ生成
- Keccak-256ハッシュによる一意識別
- 高速なハッシュマップルックアップ

**2. 動的型変換**
- ABI定義に基づく自動型変換
- Solidity型からJava型へのマッピング
- インデックス付きパラメータとデータパラメータの分離

**3. マルチコントラクト対応**
- 複数コントラクトの並行処理
- コントラクトアドレス別フィルタリング
- イベント名の重複解決

#### 3.1.2 Web3jのEventEncoder.encode()によるシグネチャ生成

**シグネチャ生成プロセス**:
```java
// 1. イベント定義からシグネチャ文字列生成
String eventSignature = "Transfer(address,address,uint256)";

// 2. Web3j EventEncoder でハッシュ生成
String eventHash = EventEncoder.encode(event);
// 結果: "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"

// 3. ログのtopics[0]と照合
if (log.getTopics().get(0).equals(eventHash)) {
    // マッチしたイベントを処理
}
```

**型マッピング例**:
| Solidity型 | ABI型 | Java型 | Web3j型 |
|-----------|-------|--------|---------|
| address | address | String | Address |
| uint256 | uint256 | BigInteger | Uint256 |
| bytes32 | bytes32 | byte[] | Bytes32 |
| string | string | String | Utf8String |
| bool | bool | Boolean | Bool |

### 3.2 イベントフィルタリング実装

#### 3.2.1 フィルタリングフロー

```mermaid
sequenceDiagram
    participant ELD as EthEventLogDao
    participant AP as AbiParser
    participant W3J as Web3j
    participant LOG as Event Log

    LOG->>ELD: Raw event log
    ELD->>ELD: Extract topics[0] (event signature)
    ELD->>AP: lookupEventBySignature(signature)
    
    alt Event signature found
        AP-->>ELD: Event definition
        ELD->>W3J: Decode indexed parameters
        ELD->>W3J: Decode non-indexed data
        W3J-->>ELD: Decoded parameters
        ELD->>ELD: Create structured Event object
        ELD-->>LOG: Structured event data
    else Event signature not found
        ELD->>ELD: Log unknown event
        ELD-->>LOG: Skip processing
    end
```

#### 3.2.2 動的イベント処理

**1. イベント定義の管理**
```java
// イベントシグネチャ → イベント定義のマッピング
Map<String, EventDefinition> eventSignatureMap = new ConcurrentHashMap<>();

// ABI解析時に登録
public void registerEvent(EventDefinition eventDef) {
    String signature = EventEncoder.encode(eventDef.getEvent());
    eventSignatureMap.put(signature, eventDef);
}
```

**2. 動的パラメータ解析**
```java
public Event parseEventLog(Log log) {
    String eventSignature = log.getTopics().get(0);
    EventDefinition eventDef = eventSignatureMap.get(eventSignature);
    
    if (eventDef == null) {
        // 未知のイベント
        return null;
    }
    
    // インデックス付きパラメータの解析
    List<String> indexedValues = parseIndexedParameters(
        log.getTopics().subList(1, log.getTopics().size()),
        eventDef.getIndexedParameters()
    );
    
    // 非インデックスデータの解析
    List<String> nonIndexedValues = parseNonIndexedData(
        log.getData(),
        eventDef.getNonIndexedParameters()
    );
    
    return Event.builder()
        .transactionHash(log.getTransactionHash())
        .logIndex(log.getLogIndex().intValue())
        .name(eventDef.getName())
        .indexedValues(indexedValues.toString())
        .nonIndexedValues(nonIndexedValues.toString())
        .blockTimestamp(block.getTimestamp().longValue())
        .log(log.toString())
        .build();
}
```

### 3.3 エラーハンドリング

#### 3.3.1 ABI解析エラー
- **JSON形式エラー**: 不正なABI JSON形式の検出・スキップ
- **型定義エラー**: 未サポート型の警告・代替処理
- **重複定義エラー**: 同一シグネチャの重複検出・後勝ち処理

#### 3.3.2 イベント処理エラー
- **デコードエラー**: パラメータデコード失敗時の代替処理
- **型変換エラー**: Java型変換失敗時のフォールバック
- **メモリエラー**: 大量イベント処理時のメモリ管理

## 4. パフォーマンス最適化

### 4.1 メモリ最適化
- **遅延ロード**: 必要時のみABI定義をロード
- **キャッシュ戦略**: 頻繁にアクセスされるイベント定義のキャッシュ
- **ガベージコレクション**: 不要なオブジェクトの適切な解放

### 4.2 処理速度最適化
- **並行処理**: 複数ABI ファイルの並行解析
- **ハッシュマップ**: O(1)でのイベントシグネチャルックアップ
- **バッチ処理**: 複数イベントの一括処理

### 4.3 スケーラビリティ
- **水平スケーリング**: 複数インスタンスでの負荷分散
- **垂直スケーリング**: メモリ・CPU リソースの動的調整
- **キューイング**: BlockingQueueによる非同期処理

この設計により、柔軟で高性能なABI統合とイベント処理システムを実現し、多様なスマートコントラクトイベントに対応可能な拡張性を確保しています。
