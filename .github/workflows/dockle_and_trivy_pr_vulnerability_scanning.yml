# 設計資料: https://decurret.atlassian.net/wiki/spaces/DIG/pages/3477700611/dockle+Trivy
name: "[PR][BCMonitoringJava]Dockle&Trivy Vulnerability Scanning CI"

on:
  pull_request:
    types: [opened, synchronize]
  workflow_dispatch:

permissions:
  contents: read
  id-token: write

jobs:
  security:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4 # 公式 https://github.com/actions/checkout
