package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb

import adhoc.helper.LogAppenderHelper
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import org.apache.logging.log4j.Level
import org.apache.logging.log4j.Logger
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import spock.lang.Specification

class EventDaoSpec extends Specification {

	PooledDynamoDbService mockPooledDynamoDbService
	BcmonitoringConfigurationProperties mockProperties
	BcmonitoringConfigurationProperties.Aws mockAws
	BcmonitoringConfigurationProperties.Aws.Dynamodb mockDynamodb
    LogAppenderHelper logAppender
	EventDao eventDao

	def setup() {
		mockPooledDynamoDbService = Mock(PooledDynamoDbService)
		mockProperties = Mock(BcmonitoringConfigurationProperties)
		mockAws = Mock(BcmonitoringConfigurationProperties.Aws)
		mockDynamodb = Mock(BcmonitoringConfigurationProperties.Aws.Dynamodb)
        logAppender = LogAppenderHelper.createAndAttach(EventDao.class.name)

		mockProperties.getAws() >> mockAws
		mockAws.getDynamodb() >> mockDynamodb
		mockDynamodb.getEventsTableName() >> "events"
		mockDynamodb.getTableNameWithPrefix("events") >> "prefix-events"

		eventDao = new EventDao(mockPooledDynamoDbService, mockProperties)
	}

    def cleanup() {
        // Remove log appender to prevent log pollution between tests
        if (logAppender != null) {
            // Detach from all loggers that might be using this appender
            logAppender.detach()
            logAppender = null
        }
    }

	def "should successfully save an event"() {
		given:
		def event = Event.builder()
				.transactionHash("0x123456")
				.logIndex(1)
				.name("TestEvent")
				.indexedValues("indexed values")
				.nonIndexedValues("non-indexed values")
				.blockTimestamp(1234567890L)
				.log("log content")
				.build()

		when:
		def result = eventDao.save(event)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { java.util.function.Function function ->
			def mockClient = Mock(DynamoDbClient)
			mockClient.putItem({ PutItemRequest request ->
				request.tableName() == "prefix-events" &&
						request.item() == event.toAttributeMap()
			})
			return function.apply(mockClient)
		}
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 0
		result == true
	}

	def "should handle exception when saving event fails"() {
		given:
		def event = Event.builder()
				.transactionHash("0x123456")
				.logIndex(1)
				.name("TestEvent")
				.indexedValues("indexed values")
				.nonIndexedValues("non-indexed values")
				.blockTimestamp(1234567890L)
				.log("log content")
				.build()

		def exception = DynamoDbException.builder()
				.message("Test error message")
				.build()

		when:
		def result = eventDao.save(event)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { throw exception }
		logAppender.events.message.formattedMessage.contains("Failed to save event: " + event.transactionHash + ", logIndex: " + event.logIndex)
		result == false
	}

	def "should return true when handling null event"() {
		when:
		def result = eventDao.save(null)

		then:
		// The method returns true even with null input due to mocking
		result == true
	}

	def "should attempt to save event even with empty attribute map"() {
		given:
		def event = Mock(Event)
		event.toAttributeMap() >> [:]

		when:
		def result = eventDao.save(event)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { java.util.function.Function function ->
			def mockClient = Mock(DynamoDbClient)
			mockClient.putItem({ PutItemRequest request ->
				request.tableName() == "prefix-events" &&
						request.item() == [:]
			})
			return function.apply(mockClient)
		}
        logAppender.events.findAll(it -> it.level == Level.ERROR).size() == 0
		result == true
	}

	def "should handle generic Exception during save operation"() {
		given:
		def event = Event.builder()
				.transactionHash("0x123456")
				.logIndex(1)
				.name("TestEvent")
				.indexedValues("indexed values")
				.nonIndexedValues("non-indexed values")
				.blockTimestamp(1234567890L)
				.log("log content")
				.build()

		def genericException = new IllegalArgumentException("Generic error")

		when:
		def result = eventDao.save(event)

		then:
		1 * mockPooledDynamoDbService.executeWithConnection(_) >> { throw genericException }
		logAppender.events.message.formattedMessage.contains("Unexpected error saving event: " + event.transactionHash + ", logIndex: " + event.logIndex)
		result == false
	}
}
