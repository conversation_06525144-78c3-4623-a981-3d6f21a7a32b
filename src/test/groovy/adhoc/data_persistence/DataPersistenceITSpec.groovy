package adhoc.data_persistence

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.doAnswer

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDao
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.DynamoDBConnectionPool
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.springframework.test.util.ReflectionTestUtils
import org.web3j.protocol.core.methods.response.EthLog
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class DataPersistenceITSpec extends BaseAdhocITSpec {

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Autowired
	BcmonitoringConfigurationProperties properties

	@MockitoSpyBean
	DynamoDBConnectionPool connectionPoolSpy

	@MockitoSpyBean
	EventDao eventDao

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"AccessCtrl",
			"Token",
			"Account",
			"Provider"
		])
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Should successfully store parsed events from both new blocks and pending transactions to DynamoDB Events table
	 * Verifies service correctly stores events from both new blocks and pending transactions
	 * Expected: All events are stored with correct names, block numbers, and transaction hashes
	 */
	def "Should successfully store parsed events from both new blocks and pending transactions to DynamoDB Events table"() {
		given: "Mock blockchain data with new blocks and pending transactions"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockLogNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		// Setup pending events: 2 role-related events from earlier block
		def pendingEvents = ["roleGranted", "roleRevoked"]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Service starts successfully and processes ABI files"
		def logMessages = logAppender.events.message.formattedMessage
		assert logMessages.any { it.contains("Started bc monitoring") }
		assert logMessages.any {
			it.contains("ABI file processed: address=") &&
					it.contains("contract_name=") &&
					it.contains("last_modified=") &&
					it.contains("events=")
		}

		and: "All 5 events are correctly stored in DynamoDB Events table"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 5, "Expected 5 events but found ${eventsInDb.size()}"

		and: "Each expected event type is present with correct metadata"
		def expectedEvents = [
			"RoleAdminChanged",
			"RoleGranted",
			"RoleRevoked",
			"AddProviderRole",
			"AddTokenByProvider"
		]

		expectedEvents.each { expectedEventName ->
			def event = eventsInDb.find { it.get("name").s() == expectedEventName }
			assert event != null, "Event '${expectedEventName}' not found in DynamoDB"

			// Verify essential fields are present
			assert event.get("transactionHash") != null, "Transaction hash missing for ${expectedEventName}"
			assert event.get("log") != null, "Log data missing for ${expectedEventName}"
			assert event.get("logIndex") != null, "Log index missing for ${expectedEventName}"
			assert event.get("blockTimestamp") != null, "Block timestamp missing for ${expectedEventName}"
		}
	}

	/**
	 * Should save block height when processing pending transactions with different block numbers
	 * Verifies service correctly saves block height when transitioning between different block numbers
	 * Expected: Block height is saved when processing transitions between different block numbers
	 */
	def "Should updates block height correctly when processing pending transactions"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		setUpEventStream(Collections.emptyList())

		// Setup mock pending events from two different blocks to trigger block height save
		def mockPendingEventLogs1 = createMockPendingEventLogs(["roleGranted"], 200L, "0xabc")
		def mockPendingEventLogs2 = createMockPendingEventLogs(["roleRevoked"], 201L, "0xdef")
		def combinedPendingEventLogs = mockPendingEventLogs1 + mockPendingEventLogs2
		setUpPendingEvent(combinedPendingEventLogs)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Block height is saved when transitioning between different block numbers"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 201L,
		"Block height 201 should be saved when transitioning from block 200 to block 201"
	}

	/**
	 * Should save block height when processing new blocks
	 * Verifies service correctly saves block height when processing new blocks
	 * Expected: Block height is saved when processing new blocks
	 */
	def "Should updates block height correctly when processing new blocks"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockLogNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		setUpPendingEvent(Collections.emptyList())

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Block height is saved when processing new blocks"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1002L,
		"Block height 1002 should be saved when processing new blocks"
	}

	/**
	 * Should handles multiple events from same block correctly when processing new blocks
	 * Verifies service correctly handles multiple events from the same block
	 * Expected: All events from the same block are correctly processed and saved to DynamoDB Events table when processing new blocks
	 */
	def "Should handles multiple events from same block correctly when processing new blocks"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1000L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1000L],
			[logType: 'roleGranted', txHash: '0x456def', blockNumber: 1000L],
			[logType: 'roleRevoked', txHash: '0x789abc', blockNumber: 1000L]
		]
		def mockNotifications = createMockLogNotifications(1000L, 1)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		setUpPendingEvent(Collections.emptyList())

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "All events from the same block are correctly processed and saved to DynamoDB Events table"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 5, "Expected 5 events but found ${eventsInDb.size()}"

		and: "Each expected event type is present with correct metadata"
		def expectedEvents = [
			"RoleAdminChanged",
			"RoleGranted",
			"RoleRevoked",
			"AddProviderRole",
			"AddTokenByProvider"
		]

		expectedEvents.each { expectedEventName ->
			def event = eventsInDb.find { it.get("name").s() == expectedEventName }
			assert event != null, "Event '${expectedEventName}' not found in DynamoDB"

			// Verify essential fields are present
			assert event.get("transactionHash") != null, "Transaction hash missing for ${expectedEventName}"
			assert event.get("log") != null, "Log data missing for ${expectedEventName}"
			assert event.get("logIndex") != null, "Log index missing for ${expectedEventName}"
			assert event.get("blockTimestamp") != null, "Block timestamp missing for ${expectedEventName}"
		}
	}

	/**
	 * Should handles multiple events from same transaction correctly when processing pending transactions
	 * Verifies service correctly handles multiple events from the same transaction
	 * Expected: All events from the same transaction are correctly processed and saved to DynamoDB Events table when processing pending transactions
	 */
	def "Should handles multiple events from same transaction correctly when processing pending transactions"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		setUpEventStream(Collections.emptyList())

		// Setup mock pending events with multiple events from the same transaction
		def pendingEvents = [
			"roleGranted",
			"roleRevoked",
			"roleAdminChanged",
			"addProviderRole",
			"addTokenByProvider"
		]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "All events from the same transaction are correctly processed and saved to DynamoDB Events table"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 5, "Expected 5 events but found ${eventsInDb.size()}"

		and: "Each expected event type is present with correct metadata"
		def expectedEvents = [
			"RoleAdminChanged",
			"RoleGranted",
			"RoleRevoked",
			"AddProviderRole",
			"AddTokenByProvider"
		]

		expectedEvents.each { expectedEventName ->
			def event = eventsInDb.find { it.get("name").s() == expectedEventName }
			assert event != null, "Event '${expectedEventName}' not found in DynamoDB"

			// Verify essential fields are present
			assert event.get("transactionHash") != null, "Transaction hash missing for ${expectedEventName}"
			assert event.get("log") != null, "Log data missing for ${expectedEventName}"
			assert event.get("logIndex") != null, "Log index missing for ${expectedEventName}"
			assert event.get("blockTimestamp") != null, "Block timestamp missing for ${expectedEventName}"
		}
	}

	/**
	 * Should save events and block height when processing new blocks
	 * Verifies service correctly saves events and block height when processing new blocks
	 * Expected: Events and block height are saved when processing new blocks
	 */
	def "Should save events and block height when processing new blocks"() {
		given: "Initial block height is 150 and all dependencies available"

		// Insert initial block height into BlockHeight table
		AdhocHelper.insertBlockHeight(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, 150L)

		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockLogNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		setUpPendingEvent(Collections.emptyList())

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The blockchain monitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Events and block height are saved when processing new blocks"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 3, "Expected 3 events but found ${eventsInDb.size()}"

		and: "Block height is saved"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1002L, "Block height should be updated to 1002"
	}

	/**
	 * Should connection pool (max 10 connections) managed correctly, operations complete successfully
	 * Verifies DynamoDB connection pool behavior matches Go version with proper connection reuse and limits
	 * Expected: All operations complete successfully with connection pool logs showing proper management
	 */
	def "Should connection pool managed correctly, operations complete successfully"() {
		given: "Multiple events that will trigger DynamoDB operations to test connection pool"

		// Setup moderate load to test connection pool behavior
		def newBlockEvents = []
		def eventTypes = [
			'addProviderRole',
			'addTokenByProvider',
			'roleAdminChanged',
			'roleGranted',
			'roleRevoked'
		]

		// Create 15 blocks with 4 events each = 60 total events (enough to stress test max 10 connections)
		for (int blockNum = 1000; blockNum < 1015; blockNum++) {
			for (int eventIdx = 0; eventIdx < 4; eventIdx++) {
				newBlockEvents << [
					logType: eventTypes[eventIdx % eventTypes.size()],
					txHash: "0x${blockNum}${eventIdx}pool",
					blockNumber: blockNum as Long
				]
			}
		}

		def mockNotifications = createMockLogNotifications(1000L, 15)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		// Setup more pending events to trigger concurrent pool operations
		def pendingEvents = []
		for (int i = 0; i < 20; i++) {
			pendingEvents << eventTypes[i % eventTypes.size()]
		}
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 500L, "0xpool")
		setUpPendingEvent(mockPendingEventLogs)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The blockchain monitoring service processes events using connection pool"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 40 seconds to allow processing of high volume
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 40, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Service starts successfully and processes events"
		def logMessages = logAppender.events.message.formattedMessage
		assert logMessages.any { it.contains("Started bc monitoring") }

		and: "Connection pool logs show proper connection management"
		assert logMessages.any { it.contains("Reused existing DynamoDB connection from pool") }
		assert logMessages.any { it.contains("Returned DynamoDB connection to pool") }

		and: "All events are successfully stored in DynamoDB"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		// Should have 60 new block events + 20 pending events = 80 total
		assert eventsInDb.size() == 80, "Expected 80 events but found ${eventsInDb.size()}"

		and: "Block height is correctly updated"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1014L, "Block height should be updated to 1014"
	}

	/**
	 * Should handles transactions with empty events when processing new blocks
	 * Verifies service correctly handles transactions with empty events
	 * Expected: Transaction of new block processed successfully, no events saved, block height updated
	 */
	def "Should handles transactions with empty events when processing new blocks"() {
		given: "New blocks with transactions but no events"
		// Setup new blocks with transactions that have no events (empty logs)
		def mockNotifications = createMockLogNotifications(1000L, 3)
		setUpEventStream(mockNotifications)

		// Setup mock Web3j to return blocks with transactions but empty event logs
		setupMockWeb3jWithEmptyEvents([1000L, 1001L, 1002L])

		setUpPendingEvent(Collections.emptyList())

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The blockchain monitoring service starts and processes blocks with empty events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		// Auto-stop service after 15 seconds to prevent infinite running
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Service starts successfully and processes blocks"
		def logMessages = logAppender.events.message.formattedMessage
		assert logMessages.any { it.contains("Started bc monitoring") }

		and: "No events are saved to DynamoDB Events table since transactions have empty events"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 0, "Expected 0 events but found ${eventsInDb.size()}"

		and: "Block height is updated even with empty events"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1, "Block height should be updated even when transactions have empty events (Go behavior)"
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1002L,
		"Block height should be updated to 1002 even when transactions have empty events"
	}

	/**
	 * Should log error and return false when EventDao.save() fails using MockitoSpyBean
	 * Uses MockitoSpyBean to mock DynamoDBConnectionPool.acquireConnection() method
	 * - Returns a mock DynamoDbClient that throws exception for 4th call
	 * - Allows normal execution for other calls
	 * Expected: Event save returns false for specific transaction, error logged, monitoring stops and retries after
	 * interval when processing new blocks
	 */
	def "Should log error and return false when EventDao save fail of new blocks using MockitoSpyBean"() {
		given: "Setup events and configure MockitoSpyBean to fail for specific transaction"
		// Setup new block events: 3 events with different transaction hashes
		def newBlockEvents = [
			[logType: 'addTokenByProvider', txHash: '0xpool001', blockNumber: 1000L],
			[logType: "addProviderRole", txHash: '0xpool002', blockNumber: 1001L],
			// This will fail
			[logType: 'roleAdminChanged', txHash: '0xpool003', blockNumber: 1002L] // This will not be processed
		]
		def mockNotifications = createMockLogNotifications(1000L, newBlockEvents.size())
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		setUpPendingEvent(Collections.emptyList())

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		// Create a counter to track calls and determine when to fail
		def callCount = new AtomicInteger(0)
		// Configure MockitoSpyBean to return a mock client that fails for specific transaction
		doAnswer { invocation ->
			def currentCall = callCount.incrementAndGet()

			// 1st call is for block height, 2nd and 3rd for register events and block height of 0xpool001
			// 4th call is for save event of new block 0xpool002
			if (currentCall == 4) {
				def mockClient = Mock(DynamoDbClient) {
					putItem(_) >> {
						throw DynamoDbException.builder()
						.message("MockitoSpyBean simulated DynamoDB failure for transaction 0xpool002")
						.statusCode(500)
						.build()
					}
				}
				return mockClient
			}

			// For other calls, use real method
			return invocation.callRealMethod()
		}.when(connectionPoolSpy).acquireConnection()

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 20 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing (service handles errors gracefully)"
		noExceptionThrown()

		and: "Error is logged for the failed event with transaction hash 0xpool002135"
		def logMessages = logAppender.events.message.formattedMessage
		assert logMessages.any {
			it.contains("Failed to save event: 0xpool002135") &&
					it.contains("logIndex:")
		}, "Expected error log message for failed event 0xpool002 not found. Actual logs: ${logMessages}"

		and: "Successful events are saved, not contain event failure"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		// Should have only 1 successful events (0xpool001134)
		assert eventsInDb.size() == 1, "Expected 1 successful events but found ${eventsInDb.size()}"

		def savedTransactionHashes = eventsInDb.collect { it.get("transactionHash").s() }
		assert savedTransactionHashes.contains("0xpool001134"), "Successful transaction 0xpool001134 should be saved"
		assert !savedTransactionHashes.contains("0xpool002135"), "Failed transaction 0xpool002135 should not be saved"

		and: "Block height updated not belong to event failure"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1000L, "Block height should be updated to 1000"
		assert blockHeightInDb[0].get("blockNumber").n().toLong() != 1001L, "Block height updated not belong to event save failure"

		and: "Service stop process and restart"
		assert logMessages.any {
			it.contains("Monitor event occurred error, restarting...")
		}, "Expected service restart but not found. Actual logs: ${logMessages}"
		assert logMessages.any {
			it.contains("Monitoring events...")
		}, "Expected service start monitoring events but not found. Actual logs: ${logMessages}"

		cleanup:
		// Clear the connection pool to remove any mock clients
		def connectionPoolField = ReflectionTestUtils.getField(connectionPoolSpy, "connectionPool")
		if (connectionPoolField != null) {
			connectionPoolField.clear()
		}
	}

	/**
	 * Should log error and return false when EventDao.save() fails using MockitoSpyBean
	 * Uses MockitoSpyBean to mock DynamoDBConnectionPool.acquireConnection() method
	 * - Returns a mock DynamoDbClient that throws exception for 4th call
	 * - Allows normal execution for other calls
	 * Expected: Event save returns false for specific transaction, error logged, monitoring stops and retries after
	 * interval when processing pending transactions
	 */
	def "Should log error and return false when EventDao save fail of pending transaction using MockitoSpyBean"() {
		given: "Setup events and configure MockitoSpyBean to fail for specific transaction"

		setUpEventStream(Collections.emptyList())

		// Setup mock pending events with same block number
		def pendingEvents = [
			"roleGranted",
			"roleRevoked",
			"roleAdminChanged"
		]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		// Create a counter to track calls and determine when to fail
		def callCount = new AtomicInteger(0)
		// Configure MockitoSpyBean to return a mock client that fails for specific transaction
		doAnswer { invocation ->
			def currentCall = callCount.incrementAndGet()

			// 1st call is for block height, 2nd and 3rd for register events of pending transaction 0xabc134, 0xabc135
			// 4th call is for save event of pending transaction 0xabc136
			if (currentCall == 4) {
				def mockClient = Mock(DynamoDbClient) {
					putItem(_) >> {
						throw DynamoDbException.builder()
						.message("MockitoSpyBean simulated DynamoDB failure for transaction 0xabc136")
						.statusCode(500)
						.build()
					}
				}
				return mockClient
			}

			// For other calls, use real method
			return invocation.callRealMethod()
		}.when(connectionPoolSpy).acquireConnection()

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 20 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing (service handles errors gracefully)"
		noExceptionThrown()

		and: "Error is logged for the failed event with transaction hash 0xabc136"
		def logMessages = logAppender.events.message.formattedMessage
		assert logMessages.any {
			it.contains("Failed to save event: 0xabc136") &&
					it.contains("logIndex:")
		}, "Expected error log message for failed pending transaction 0xabc136 not found. Actual logs: ${logMessages}"
		assert logMessages.any {
			it.contains("Error while processing pending transactions: Failed to save transaction")
		}, "Expected log Error while processing pending transactions not found. Actual logs: ${logMessages}"

		and: "Successful events are saved, not contain event failure"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		// Should have only 1 successful events (0xabc134, 0xabc135)
		assert eventsInDb.size() == 2, "Expected 2 successful events but found ${eventsInDb.size()}"

		def savedTransactionHashes = eventsInDb.collect { it.get("transactionHash").s() }
		assert savedTransactionHashes.contains("0xabc134"), "Successful transaction 0xabc134 should be saved"
		assert savedTransactionHashes.contains("0xabc135"), "Successful transaction 0xabc135 should be saved"
		assert !savedTransactionHashes.contains("0xabc136"), "Failed transaction 0xabc136 should not be saved"

		and: "Block height updated not belong to event failure"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 0, "Expected no successful block height but found ${blockHeightInDb.size()}"

		and: "Service stop process and restart"
		assert logMessages.any {
			it.contains("Monitor event occurred error, restarting...")
		}, "Expected service restart but not found. Actual logs: ${logMessages}"

		cleanup:
		// Clear the connection pool to remove any mock clients
		def connectionPoolField = ReflectionTestUtils.getField(connectionPoolSpy, "connectionPool")
		if (connectionPoolField != null) {
			connectionPoolField.clear()
		}
	}

	/**
	 * Should log error and restart when BlockHeightDao.get() fails using MockitoSpyBean
	 * Uses MockitoSpyBean to mock DynamoDBConnectionPool.acquireConnection() method
	 * - Returns a mock DynamoDbClient that throws exception for 1st call query
	 * Expected: Error logged "Error retrieving block height from DynamoDB", monitoring stops and retries after interval
	 */
	def "Should log error and restart when BlockHeightDao get block fail using MockitoSpyBean"() {
		given: "Setup events and configure MockitoSpyBean to fail for specific BlockHeight"

		// Insert initial block height into BlockHeight table
		AdhocHelper.insertBlockHeight(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, 5L)

		// Configure MockitoSpyBean to return a mock client that fails for specific transaction
		doAnswer { invocation ->
			def mockClient = Mock(DynamoDbClient) {
				query(_) >> {
					throw DynamoDbException.builder()
					.message("MockitoSpyBean simulated DynamoDB failure when query get BlockHeight")
					.statusCode(500)
					.build()
				}
			}
			return mockClient
		}.when(connectionPoolSpy).acquireConnection()

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 10 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing (service handles errors gracefully)"
		noExceptionThrown()

		def logMessages = logAppender.events.message.formattedMessage

		and: "Error retrieving block height from DynamoDB"
		assert logMessages.any {
			it.contains("Error retrieving block height from DynamoDB")
		}, "Error retrieving block height from DynamoDB not found. Actual logs: ${logMessages}"

		and: "Service stop process and restart"
		assert logMessages.any {
			it.contains("Monitor event occurred error, restarting...")
		}, "Expected service restart but not found. Actual logs: ${logMessages}"
		assert logMessages.any {
			it.contains("Monitoring events...")
		}, "Expected service start monitoring events but not found. Actual logs: ${logMessages}"

		cleanup:
		// Clear the connection pool to remove any mock clients
		def connectionPoolField = ReflectionTestUtils.getField(connectionPoolSpy, "connectionPool")
		if (connectionPoolField != null) {
			connectionPoolField.clear()
		}
	}

	/**
	 * Should log error and return false when BlockHeightDao.save() fails using MockitoSpyBean
	 * Uses MockitoSpyBean to mock DynamoDBConnectionPool.acquireConnection() method
	 * - Returns a mock DynamoDbClient that throws DynamoDbException for 5th call
	 * - Allows normal execution for other calls
	 * Expected: Block height save returns false for specific transaction, error logged, monitoring stops
	 * and retries after interval when processing new blocks
	 */
	def "Should log error and restart when BlockHeightDao save block height of new block fail using MockitoSpyBean"() {
		given: "Setup events and configure MockitoSpyBean to fail for specific BlockHeight"

		// Setup new block events: 3 events with different transaction hashes
		def newBlockEvents = [
			[logType: 'addTokenByProvider', txHash: '0xpool001', blockNumber: 1000L],
			[logType: "addProviderRole", txHash: '0xpool002', blockNumber: 1001L],
			// This will fail
			[logType: 'roleAdminChanged', txHash: '0xpool003', blockNumber: 1002L] // This will not be processed
		]
		def mockNotifications = createMockLogNotifications(1000L, newBlockEvents.size())
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		setUpPendingEvent(Collections.emptyList())

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		// Create a counter to track calls and determine when to fail
		def callCount = new AtomicInteger(0)
		// Configure MockitoSpyBean to return a mock client that fails for specific transaction
		doAnswer { invocation ->
			def currentCall = callCount.incrementAndGet()

			// 1st call is for block height, 2nd and 3rd for register events and block height of 0xpool001
			// 4th for register event 0xpool002 belong to Block 1001L
			// 5th for save block height 1001
			if (currentCall == 5) {
				def mockClient = Mock(DynamoDbClient) {
					putItem(_) >> {
						throw DynamoDbException.builder()
						.message("MockitoSpyBean simulated DynamoDB failure for transaction 0xpool002")
						.build()
					}
				}
				return mockClient
			}

			// For other calls, use real method
			return invocation.callRealMethod()
		}.when(connectionPoolSpy).acquireConnection()

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 20 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		def logMessages = logAppender.events.message.formattedMessage

		and: "Error is logged for the failed event with transaction hash 0xpool002135"
		assert logMessages.any {
			it.contains("Failed to save block height: 1001")
		}, "Expected error log message for block height 1001 not found. Actual logs: ${logMessages}"

		and: "Successful events are saved, even contain event belong to block height failure"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		// Should have 2 successful events (0xpool001134, 0xpool002135)
		assert eventsInDb.size() == 2, "Expected 2 successful events but found ${eventsInDb.size()}"

		def savedTransactionHashes = eventsInDb.collect { it.get("transactionHash").s() }
		assert savedTransactionHashes.contains("0xpool001134"), "Successful transaction 0xpool001134 should be saved"
		assert savedTransactionHashes.contains("0xpool002135"), "Successful transaction 0xpool001135 should be saved"

		and: "Block height updated not include block height failure"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 1000L, "Block height should be updated to 1000"
		assert blockHeightInDb[0].get("blockNumber").n().toLong() != 1001L, "Block height should not be updated to 1001"

		and: "Service stop process and restart"
		assert logMessages.any {
			it.contains("Monitor event occurred error, restarting...")
		}, "Expected service restart but not found. Actual logs: ${logMessages}"
		assert logMessages.any {
			it.contains("Monitoring events...")
		}, "Expected service start monitoring events but not found. Actual logs: ${logMessages}"

		cleanup:
		// Clear the connection pool to remove any mock clients
		def connectionPoolField = ReflectionTestUtils.getField(connectionPoolSpy, "connectionPool")
		if (connectionPoolField != null) {
			connectionPoolField.clear()
		}
	}

	/**
	 * Should log error and return false when BlockHeightDao.save() fails using MockitoSpyBean
	 * Uses MockitoSpyBean to mock DynamoDBConnectionPool.acquireConnection() method
	 * - Returns a mock DynamoDbClient that throws DynamoDbException for 6th call
	 * - Allows normal execution for other calls
	 * Expected: Block height save returns false for specific transaction, error logged, monitoring stops
	 * and retries after interval when processing pending transactions
	 */
	def "Should log error and restart when BlockHeightDao save block height of pending transaction fail using MockitoSpyBean"() {
		given: "Setup events and configure MockitoSpyBean to fail for specific BlockHeight"

		setUpEventStream(Collections.emptyList())
		// Insert initial block height into BlockHeight table
		AdhocHelper.insertBlockHeight(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, 5L)

		// Setup mock pending events from two different blocks to trigger block height save
		def mockPendingEventLogs1 = createMockPendingEventLogs([
			"roleGranted",
			"roleAdminChanged"
		], 100L, "0xabc100")
		def mockPendingEventLogs2 = createMockPendingEventLogs(["roleRevoked"], 101L, "0xabc101") // This will fail
		def mockPendingEventLogs3 = createMockPendingEventLogs(["addTokenByProvider"], 102L, "0xabc102") // This will not be processed
		def combinedPendingEventLogs = mockPendingEventLogs1 + mockPendingEventLogs2 + mockPendingEventLogs3
		setUpPendingEvent(combinedPendingEventLogs as List<EthLog.LogResult>)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		// Create a counter to track calls and determine when to fail
		def callCount = new AtomicInteger(0)
		// Configure MockitoSpyBean to return a mock client that fails for specific transaction
		doAnswer { invocation ->
			def currentCall = callCount.incrementAndGet()

			// 1st call is for block height, 2nd and 3rd for register events 0xabc100134, 0xabc100135
			// 4th for save block height 100
			// 5th for register event 0xabc101134
			// 6th for register pending transaction block height 101
			if (currentCall == 6) {
				def mockClient = Mock(DynamoDbClient) {
					putItem(_) >> {
						throw DynamoDbException.builder()
						.message("MockitoSpyBean simulated DynamoDB failure for transaction")
						.build()
					}
				}
				return mockClient
			}

			// For other calls, use real method
			return invocation.callRealMethod()
		}.when(connectionPoolSpy).acquireConnection()

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 20 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		def logMessages = logAppender.events.message.formattedMessage

		and: "Error is logged for the failed event with transaction hash 0xpool002135"
		assert logMessages.any {
			it.contains("Failed to save block height: 101")
		}, "Expected error log message for block height 101 not found. Actual logs: ${logMessages}"

		and: "Successful events are saved, even contain event belong to block height failure"
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		// Should have 1 successful events (0xabc100134, 0xabc100135, 0xabc101134)
		assert eventsInDb.size() == 3, "Expected 1 successful events but found ${eventsInDb.size()}"

		def savedTransactionHashes = eventsInDb.collect { it.get("transactionHash").s() }
		assert savedTransactionHashes.contains("0xabc100134"), "Successful transaction 0xabc100134 should be saved"
		assert savedTransactionHashes.contains("0xabc100135"), "Successful transaction 0xabc100135 should be saved"
		assert savedTransactionHashes.contains("0xabc101134"), "Successful transaction 0xabc101134 should be saved"

		and: "Block height updated not include block height failure"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb.size() == 1, "Expected 1 successful block height but found ${eventsInDb.size()}"
		assert blockHeightInDb[0].get("blockNumber").n().toLong() == 100L, "Block height should be updated to 100"
		assert blockHeightInDb[0].get("blockNumber").n().toLong() != 101L, "Block height should be updated to 101"

		and: "Service stop process and restart"
		assert logMessages.any {
			it.contains("Monitor event occurred error, restarting...")
		}, "Expected service restart but not found. Actual logs: ${logMessages}"
		assert logMessages.any {
			it.contains("Monitoring events...")
		}, "Expected service start monitoring events but not found. Actual logs: ${logMessages}"

		cleanup:
		// Clear the connection pool to remove any mock clients
		def connectionPoolField = ReflectionTestUtils.getField(connectionPoolSpy, "connectionPool")
		if (connectionPoolField != null) {
			connectionPoolField.clear()
		}
	}

	/**
	 * Should detects and save correct block height number for new and pending transactions.
	 * Expected block height of last new transaction saved in database
	 */
	def "Should detects and save correct block height number for new and pending transactions"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		// Setup mock new events
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockLogNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
				[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
				[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
				[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
				"addProviderRole",
				"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Provider events are processed and saved to DynamoDB"
		// Check if events were saved to DynamoDB
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 5 // addProviderRole, addTokenByProvider event

		and: "Block height is updated"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb != null
		assert blockHeightInDb.get(0).get("blockNumber").n() == "1002" // equal last blockNumber mock at eventLogConfigs
	}

	/**
	 * Should detects and save correct block height number for same block number pending transactions
	 * Expected when pending transaction save success then block height number save last block number.
	 */
	def "Should detects and save correct block height number for same block number pending transactions"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		// Setup mock new events
		setUpEventStream(Collections.emptyList())

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs([
				"addProviderRole",
				"addTokenByProvider"
		], 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Provider events are processed and saved to DynamoDB"
		// Check if events were saved to DynamoDB
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 2 // 2 events mock at mockPendingEventLogs

		and: "Block height is updated"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb != null
		assert blockHeightInDb.get(0).get("blockNumber").n() == "200" // equal blockNumber mock at mockPendingEventLogs
	}

	/**
	 * Should detects and save last block height number for different block number pending transactions
	 * Expected when pending transaction save success then block height number save last block number.
	 */
	def "Should detects and save last block height number for different block number pending transactions"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		// Setup mock new events
		setUpEventStream(Collections.emptyList())

		// Setup mock pending events
		def mockPendingEventLogs1 = createMockPendingEventLogs([
				"addProviderRole",
				"addTokenByProvider"
		], 200L, "0xabc")
		def mockPendingEventLogs2 = createMockPendingEventLogs([
				"roleRevoked",
				"modProvider"
		], 300L, "0xdef")
		def combinedPendingEventLogs = mockPendingEventLogs1 + mockPendingEventLogs2
		setUpPendingEvent(combinedPendingEventLogs as List<EthLog.LogResult>)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Provider events are processed and saved to DynamoDB"
		// Check if events were saved to DynamoDB
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 4 // 4 events mock at mockPendingEventLogs1 and mockPendingEventLogs2

		and: "Block height is updated"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb != null
		assert blockHeightInDb.get(0).get("blockNumber").n() == "300" // equal blockNumber mock at mockPendingEventLogs2
	}

	/**
	 * Should not save block height number when any pending transaction in same block fails
	 * Expected when pending transaction save fail then block height number do not save.
	 * When restart service, will query again block height to process again pending transaction
	 */
	def "Should not save block height number when any pending transaction in same block fails"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"
		// Setup mock new events
		setUpEventStream(Collections.emptyList())

		// Setup mock pending events
		def mockPendingEventLogs1 = createMockPendingEventLogs([
				"addProviderRole",
				"addTokenByProvider"
		], 200L, "0xabc")
		def mockPendingEventLogs2 = createMockPendingEventLogs([
				"roleRevoked",
				"modProvider",
				"modAccount"
		], 300L, "0xdef")
		def combinedPendingEventLogs = mockPendingEventLogs1 + mockPendingEventLogs2
		setUpPendingEvent(combinedPendingEventLogs as List<EthLog.LogResult>)

        //Mock current block number
        mockGetCurrentBlockNumber(BigInteger.valueOf(2000L))

		// Mock when 4th times call save transaction fail ~ modProvider event fail
		and: "Mock when 4th times call save transaction fail"
		def saveCallCount = new AtomicInteger(0)
		doAnswer { invocation ->
			if (saveCallCount.incrementAndGet() >= 4) {
				return false // Mock 4 times call : false
			} else {
				return invocation.callRealMethod()
			}
		}.when(eventDao).save(any(Event))

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service starts and processes ABI files successfully"
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Monitoring events...") }

		and: "Provider events are processed and saved to DynamoDB"
		// Check if events were saved to DynamoDB
		def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
		assert eventsInDb.size() == 3 // 2 events mock at mockPendingEventLogs1 and 1 event mock mockPendingEventLogs2

		and: "Block height is updated correct"
		def blockHeightInDb = AdhocHelper.scanBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		assert blockHeightInDb != null
		assert blockHeightInDb.get(0).get("blockNumber").n() == "200" // equal blockNumber mock at mockPendingEventLogs1

		and: "Server log restarting"
		assert messages.any { it.contains("Monitor event occurred error, restarting...") }
	}
}
