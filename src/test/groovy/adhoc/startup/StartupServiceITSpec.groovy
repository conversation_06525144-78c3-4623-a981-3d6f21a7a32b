package adhoc.startup

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.PooledDynamoDbService
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.springframework.test.util.ReflectionTestUtils

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class StartupServiceITSpec extends BaseAdhocITSpec {

	@Autowired
	DownloadAbiService downloadAbiService

	@Autowired
	MonitorEventService monitorEventService

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Autowired
	BcmonitoringConfigurationProperties config

	@Autowired
	BlockHeightDao blockHeightDao

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"Token",
			"Account",
			"Provider"
		])
	}

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Successful Service Startup
	 * Verifies service starts successfully with all dependencies available
	 * Expected: Service logs "Starting bc monitoring" and "Started bc monitoring"
	 */
	def "Should start successfully with all dependencies available"() {
		given: "Valid environment with accessible dependencies"

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		when: "Testing real service startup with CommandLineRunner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "Service should start successfully and log the required messages"
		noExceptionThrown()
		logAppender.events.message.formattedMessage.contains("Started bc monitoring")
		and: "Real services should be available"
		assert downloadAbiService
		assert monitorEventService
		assert s3Client
		assert dynamoDbClient
	}

	/**
	 * Service reinitialize after error occurs
	 * Verifies service automatically reinitialize monitoring after error occurs
	 * Expected: Service reinitialize and logs "Monitor event occurred error, retrying..."
	 */
	def "Should automatically reinitialize monitoring if error occurs"() {
		given: "Valid environment with accessible dependencies"

		//Not setup websocket event stream and pending event

		when: "Testing real service startup with CommandLineRunner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exception is thrown"
		noExceptionThrown()

		and: "Critical services are initialized"
		assert downloadAbiService
		assert monitorEventService
		assert dynamoDbClient
		assert s3Client

		and: "Log should indicate successful startup"
		logAppender.events.message.formattedMessage.contains("Monitor event occurred error, retrying...")
	}

	/**
	 * Service Startup with Empty ABI Bucket
	 * Verifies service starts successfully when S3 bucket exists but contains no ABI files
	 * Expected: Service starts with no contract addresses loaded, application continues running and logs "Started bc monitoring"
	 */
	def "Should start successfully with empty ABI bucket"() {
		given: "An empty S3 bucket and all dependencies available"

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		clearS3Bucket()
		assert s3Client.listObjectsV2 { it.bucket(TEST_BUCKET) }.contents().isEmpty()

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exception is thrown"
		noExceptionThrown()

		and: "Critical services are initialized"
		assert downloadAbiService
		assert monitorEventService
		assert dynamoDbClient
		assert s3Client

		and: "Log should indicate successful startup"
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Get blockheight: 0") }
		assert messages.any { it.contains("Started bc monitoring") }
	}

	/**
	 * Service startup with DynamoDB BlockHeight table is empty
	 * Verifies service starts successfully
	 * Expected: Service starts monitoring from block 0, application continues running and logs "Started bc monitoring"
	 */
	def "Should start successfully with empty DynamoDB BlockHeight table"() {
		given: "An empty DynamoDB BlockHeight and all dependencies available"

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])
		assert dynamoDbClient.scan { it.tableName(BLOCK_HEIGHT_TABLE) }.items().isEmpty()

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exception is thrown"
		noExceptionThrown()

		and: "Critical services are initialized"
		assert downloadAbiService
		assert monitorEventService
		assert dynamoDbClient
		assert s3Client

		and: "Log should indicate successful startup"
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Get blockheight: 0") }
		assert messages.any { it.contains("Started bc monitoring") }
	}

	/**
	 * Should fails to start when required properties are invalid
	 * Verifies service fails
	 * Expected: Service logs "Error starting bc monitoring"
	 */
	def "Should fails to start when required properties are invalid"() {
		given:
		config.aws.s3.setBucketName(null)

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")
		config.aws.s3.setBucketName(TEST_BUCKET)
		then:
		def messages = logAppender.events.message.formattedMessage
		assert messages.any { it.contains("Error starting bc monitoring") }
	}

	/**
	 * Should stop and log restart if DynamoDB connection Error
	 * Verifies restart application
	 * Expected: Service logs "Failed to get blockheight:" and "Restarting bc monitoring"
	 */
	def "Should stop and log restart if DynamoDB connection Error"() {
		given: "Invalid DynamoDB connection pool"
		// Get the original pooled service to restore later
		def originalPooledService = ReflectionTestUtils.getField(blockHeightDao, "pooledDynamoDbService")

		// Create a mock pooled service that will throw connection errors
		def invalidPooledService = Mock(PooledDynamoDbService) {
			executeWithConnection(_) >> {
				throw new RuntimeException("Unable to connect to DynamoDB endpoint",
				new ConnectException("Connection refused"))
			}
		}

		// Inject the invalid pooled service
		ReflectionTestUtils.setField(blockHeightDao, "pooledDynamoDbService", invalidPooledService)

		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		when: "Testing real service startup with CommandLineRunner"
		// Get the CommandLineRunner bean and execute it to trigger the logs
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)
		scheduler.schedule( {
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		// Reset the pooled service to the original one after the test
		ReflectionTestUtils.setField(blockHeightDao, "pooledDynamoDbService", originalPooledService)

		then: "Service should keep restart due to DynamoDB connection error"
		logAppender.events.message.any { it.formattedMessage.contains("Failed to get blockheight:") }
		logAppender.events.message.formattedMessage.contains("Monitor event occurred error, restarting...")
	}
}
