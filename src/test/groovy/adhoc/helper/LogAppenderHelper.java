package adhoc.helper;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.Layout;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.appender.AbstractAppender;
import org.apache.logging.log4j.core.appender.AppenderLoggingException;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.LoggerConfig;
import org.apache.logging.log4j.core.config.Property;

/**
 * Test appender collect log events for tests. Ensure start before use and can be detached cleanly
 * after tests.
 */
public class LogAppenderHelper extends AbstractAppender {

  public final List<LogEvent> events = Collections.synchronizedList(new ArrayList<>());

  // Default appender name
  private static final String DEFAULT_APPENDER_NAME = "TestAppender";

  protected LogAppenderHelper(
      String name, Filter filter, Layout<? extends Serializable> layout, boolean ignoreExceptions) {
    super(name, filter, layout, ignoreExceptions, Property.EMPTY_ARRAY);
  }

  @Override
  public void append(LogEvent event) {
    if (!isStarted()) {
      throw new AppenderLoggingException(
          "Attempted to append to non-started appender " + getName());
    }
    events.add(event.toImmutable());
  }

  /**
   * Create and attach the appender into LoggerConfig with name loggerName. Appender will be started
   * and configuration will be updated immediately.
   */
  public static LogAppenderHelper createAndAttach(String loggerName) {
    LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
    Configuration config = ctx.getConfiguration();

    LogAppenderHelper appender = new LogAppenderHelper(DEFAULT_APPENDER_NAME, null, null, true);
    appender.start(); // Important: start before add

    // Add appender to configuration
    config.addAppender(appender);

    // Get current LoggerConfig for loggerName
    LoggerConfig loggerConfig = config.getLoggerConfig(loggerName);

    // If loggerConfig return not is loggerName, create a new LoggerConfig
    if (!loggerConfig.getName().equals(loggerName)) {
      LoggerConfig childConfig = new LoggerConfig(loggerName, Level.ALL, true);
      config.addLogger(loggerName, childConfig);
      loggerConfig = childConfig;
    }

    // Add appender into LoggerConfig
    loggerConfig.addAppender(appender, null, null);

    // Apply changes
    ctx.updateLoggers();

    return appender;
  }

  /** Detach appender to all LoggerConfig, remove from configuration, then stop. */
  public void detach() {
    LoggerContext ctx = (LoggerContext) LogManager.getContext(false);
    Configuration config = ctx.getConfiguration();

    // Detach appender from all logger configs
    for (LoggerConfig lc : config.getLoggers().values()) {
      lc.removeAppender(getName());
    }
    // Detach appender from root logger (to prevent from root logger)
    config.getRootLogger().removeAppender(getName());

    // Remove appender from configuration
    config.getAppenders().remove(getName());

    // Stop appender
    stop();

    // Apply changes
    ctx.updateLoggers();
  }
}
