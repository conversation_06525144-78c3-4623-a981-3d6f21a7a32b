package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.exception.Web3jConnectionException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Configuration;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.websocket.WebSocketService;

@Configuration
@RequiredArgsConstructor
@Log4j2
public class Web3jConfig {

  private static final String WEBSOCKET_URI_TEMPLATE = "ws://%s:%s";

  private final BcmonitoringConfigurationProperties properties;
  private Web3j web3j;
  private Web3j web3jCaller;

  /**
   * Gets the cached Web3j instance, creating it if necessary.
   *
   * @return The cached Web3j instance
   * @throws Web3jConnectionException if connection fails
   */
  public synchronized Web3j getWeb3j() throws Web3jConnectionException {
    if (web3j == null) {
      web3j = createWebSocketWeb3j();
    }
    return web3j;
  }

  /**
   * Gets the cached Web3j instance for caller, creating it if necessary.
   *
   * @return The cached Web3j instance
   * @throws Web3jConnectionException if connection fails
   */
  public synchronized Web3j getWeb3jCaller() throws Web3jConnectionException {
    if (web3jCaller == null) {
      web3jCaller = createWebSocketWeb3j();
    }
    return web3jCaller;
  }

  /**
   * Creates a new WebSocket Web3j connection. This method should be used for each operation that
   * requires a fresh connection.
   *
   * @return A new Web3j instance connected via WebSocket
   * @throws Web3jConnectionException if the connection fails
   */
  public synchronized Web3j createWebSocketWeb3j() throws Web3jConnectionException {
    try {
      String wsEndpoint =
          String.format(
              WEBSOCKET_URI_TEMPLATE,
              properties.getWebsocket().getUri().getHost(),
              properties.getWebsocket().getUri().getPort());

      WebSocketService webSocketService = new WebSocketService(wsEndpoint, true);
      webSocketService.connect();

      return Web3j.build(webSocketService);
    } catch (Exception e) {
      log.error("Failed to create WebSocket Web3j connection", e);
      throw new Web3jConnectionException("Failed to create WebSocket Web3j connection", e);
    }
  }

  /**
   * Shuts down the cached Web3j instance.
   *
   * @throws Web3jConnectionException if the shutdown fails
   */
  public synchronized void shutdownWeb3j() {
    if (web3j != null) {
      try {
        web3j.shutdown();
        web3j = null;
        log.info("Successfully shut down cached Web3j connection");
      } catch (Exception e) {
        log.error("Error shutting down cached Web3j connection", e);
      }
    }
    if (web3jCaller != null) {
      try {
        web3jCaller.shutdown();
        web3jCaller = null;
        log.info("Successfully shut down cached Web3j caller connection");
      } catch (Exception e) {
        log.error("Error shutting down cached Web3j caller connection", e);
      }
    }
  }
}
