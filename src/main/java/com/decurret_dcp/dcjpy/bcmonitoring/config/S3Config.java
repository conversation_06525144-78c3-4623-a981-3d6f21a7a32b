package com.decurret_dcp.dcjpy.bcmonitoring.config;

import static com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst.LOCAL;
import static com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst.PROD;

import java.net.URI;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.core.retry.RetryMode;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;

@Configuration
@Log4j2
public class S3Config {

  private final BcmonitoringConfigurationProperties configProperties;
  private final AwsCredentialsProvider credentialsProvider;

  public S3Config(
      BcmonitoringConfigurationProperties configProperties,
      AwsCredentialsProvider credentialsProvider) {
    this.configProperties = configProperties;
    this.credentialsProvider = credentialsProvider;
  }

  /**
   * Configures the S3 client based on the environment settings.
   *
   * <p>For local development, it uses LocalStack credentials. For production, it uses the default
   * credentials provider. For other environments, it uses the configured AWS credentials.
   *
   * @return S3Client
   */
  @Bean
  public S3Client s3Client() {
    // Configure retry strategy for better resilience
    ClientOverrideConfiguration clientConfig =
        ClientOverrideConfiguration.builder().retryStrategy(RetryMode.STANDARD).build();

    S3ClientBuilder builder =
        S3Client.builder()
            .overrideConfiguration(clientConfig)
            .credentialsProvider(credentialsProvider);

    if (PROD.equals(configProperties.getEnv())) {
      // Production environment configuration
      String region = configProperties.getAws().getRegion();
      log.info("Configuring S3 client for production environment with region: {}", region);
      return builder.region(Region.of(region)).build();
    } else if (LOCAL.equals(configProperties.getEnv())) {
      // Local environment configuration with LocalStack
      String endpoint = configProperties.getLocalstack().getEndpoint();
      String region = configProperties.getLocalstack().getRegion();

      log.info(
          "Configuring S3 client for local environment with endpoint: {} and region: {}",
          endpoint,
          region);

      if (endpoint == null || endpoint.isEmpty()) {
        log.error("LocalStack endpoint is not configured properly");
        endpoint = "http://localhost:4566"; // Default LocalStack endpoint
        log.info("Using default LocalStack endpoint: {}", endpoint);
      }

      return builder
          .endpointOverride(URI.create(endpoint))
          .region(Region.of(region))
          .forcePathStyle(true) // Use path-style access for LocalStack
          .build();
    } else {
      // Other environments (dev, staging, etc.)
      String region = configProperties.getAws().getRegion();
      log.info(
          "Configuring S3 client for environment: {} with region: {}",
          configProperties.getEnv(),
          region);
      return builder.region(Region.of(region)).build();
    }
  }
}
