package com.decurret_dcp.dcjpy.bcmonitoring.config;

import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "")
@Data
public class BcmonitoringConfigurationProperties {
  private Server server;
  private Aws aws;
  private Ethereum ethereum;
  private Websocket websocket;
  private Subscription subscription;
  private LocalStack localstack;
  private String env;
  private String abiFormat;
  private boolean eagerStart;

  @Data
  public static class Server {
    private int port;
  }

  @Data
  public static class Aws {
    private String region;
    private String accessKeyId;
    private String secretAccessKey;
    private Dynamodb dynamodb;
    private S3 s3;

    @Data
    public static class Dynamodb {
      private String region;
      private String tablePrefix;
      private String endpoint;
      private String eventsTableName;
      private String blockHeightTableName;

      /**
       * Get the table name with prefix.
       *
       * @param tableName The table name to be prefixed.
       * @return The table name with prefix.
       */
      public String getTableNameWithPrefix(@NotNull String tableName) {
        if (tablePrefix == null || tablePrefix.isEmpty()) {
          return tableName;
        }
        return tablePrefix + "-" + tableName;
      }
    }

    @Data
    public static class S3 {
      private String bucketName;
      private String region;
    }
  }

  @Data
  public static class Ethereum {
    private String endpoint;
    private Long maxLogsRange;
  }

  @Data
  public static class Websocket {
    private Uri uri;

    @Data
    public static class Uri {
      private String host;
      private String port;
    }
  }

  @Data
  public static class Subscription {
    private String checkInterval;
    private String allowableBlockTimestampDiffSec;
  }

  @Data
  public static class LocalStack {
    private String endpoint;
    private String accessKey;
    private String secretKey;
    private String region;
  }
}
