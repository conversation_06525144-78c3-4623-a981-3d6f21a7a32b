package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService;
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.AbiParserException;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.ConfigurationException;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.ExceptionUtil;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3CommonPrefixesListingException;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3Exception;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Log4j2
public class MonitoringRunnerConfig {

  private final MonitorEventService monitorEventService;
  private final DownloadAbiService downloadAbiService;
  private final BcmonitoringConfigurationProperties properties;

  public MonitoringRunnerConfig(
      MonitorEventService monitorEventService,
      DownloadAbiService downloadAbiService,
      BcmonitoringConfigurationProperties properties) {
    this.monitorEventService = monitorEventService;
    this.downloadAbiService = downloadAbiService;
    this.properties = properties;
  }

  /**
   * This method checks if monitoring should start immediately: - If the eagerStart flag is set in
   * the properties + eagerStart is typically a configuration property used to control whether a
   * service or component should start automatically when the application boots up. - If the command
   * line argument "-f" is provided + The -f is a command-line argument used as a manual override to
   * start the monitoring process when the application runs. It's an alternative to setting
   * eagerStart=true in your configuration.
   */
  @Bean
  public CommandLineRunner commandLineRunner() {
    return args -> {
      if (properties.isEagerStart()
          || (args != null && args.length > 0 && Objects.equals(args[0], "-f"))) {
        startBCMonitoring();
      }
    };
  }

  /**
   * This method is used to start the monitoring process. It will run in a virtual thread and will
   * monitor events. When downloading ABI failure, it will log a fatal error and exit. When
   * monitoring events failure with retryable exception, it will log a warning and retry monitoring.
   * When monitoring events failure with a non-retryable exception, it will log an error and exit.
   */
  private void startBCMonitoring() {
    log.info("Starting bc monitoring");
    try {
      downloadAbiService.execute();
      log.info("Started bc monitoring");
      // Start monitoring events
      while (ContextConfig.isServiceRunning()) {
        try {
          // Generate process UUID once per monitoring process
          String processUuid = java.util.UUID.randomUUID().toString();
          org.apache.logging.log4j.ThreadContext.put("process_uuid", processUuid);
          log.info("Monitoring events...");
          monitorEventService.execute();
        } catch (Exception e) {
          if (ExceptionUtil.isRetryable(e)) {
            log.warn("Monitor event occurred error, retrying...", e);
            org.apache.logging.log4j.ThreadContext.remove("process_uuid");
          } else {
            throw e;
          }
        }
      }
    } catch (AbiParserException
        | S3Exception
        | S3CommonPrefixesListingException
        | ConfigurationException e) {
      // Handle exceptions that occur when downloading ABI
      log.fatal("Error starting bc monitoring", e);
    } catch (Exception e) {
      // Handle any other unexpected exceptions occurred in the monitor event
      log.error("Monitor event occurred error, restarting...", e);
    }
  }
}
