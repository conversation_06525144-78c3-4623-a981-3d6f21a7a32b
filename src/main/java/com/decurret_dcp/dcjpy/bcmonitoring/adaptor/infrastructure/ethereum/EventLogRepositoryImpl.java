package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.BlockchainException;
import java.io.IOException;
import java.math.BigInteger;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Repository;

/** Repository implementation for Ethereum event logs */
@Repository
@Log4j2
public class EventLogRepositoryImpl implements EventLogRepository {
  private final EthEventLogDao eventLogDao;

  public EventLogRepositoryImpl(EthEventLogDao eventLogDao) {
    this.eventLogDao = eventLogDao;
  }

  /**
   * Subscribe to blockchain events
   *
   * @return Queue of transactions containing events
   * @throws BlockchainException if there is an error subscribing to events
   */
  @Override
  public BlockingQueue<Transaction> subscribe() {
    try {
      return eventLogDao.subscribeAll();
    } catch (Exception e) {
      String errorMessage = "Error subscribing to blockchain events";
      log.error(errorMessage, e);
      throw new BlockchainException(errorMessage, e);
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param startBlock Block number to start from
   * @param endBlock Block number to end at
   * @return List of transactions containing events
   * @throws BlockchainException if there is an error getting filter logs
   */
  @Override
  public List<Transaction> getFilterLogs(BigInteger startBlock, BigInteger endBlock) {
    try {
      return eventLogDao.getPendingTransactions(startBlock, endBlock);
    } catch (Exception e) {
      String errorMessage =
          "Error getting filter logs from block height: " + startBlock + " to " + endBlock;
      log.error(errorMessage, e);
      throw new BlockchainException(errorMessage, e);
    }
  }

  /**
   * Get the current block number
   *
   * @return Current block number
   * @throws IOException if there is an error getting the current block number
   */
  @Override
  public BigInteger getCurrentBlockNumber() throws IOException {
    return eventLogDao.getCurrentBlockNumber();
  }
}
