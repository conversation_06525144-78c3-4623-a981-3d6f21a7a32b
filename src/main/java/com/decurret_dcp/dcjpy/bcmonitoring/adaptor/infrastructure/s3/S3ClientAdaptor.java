package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3CommonPrefixesListingException;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3Exception;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

@Component
@Log4j2
public class S3ClientAdaptor implements S3AbiRepository {
  private final S3Client s3Client;

  public S3ClientAdaptor(S3Client s3Client) {
    this.s3Client = s3Client;
  }

  /**
   * List common prefixes of objects in a bucket.
   *
   * @param bucketName The name of the S3 bucket.
   * @param delimiter The delimiter to use for grouping objects.
   * @return A list of common prefixes.
   * @throws S3CommonPrefixesListingException if an error occurs while listing objects.
   */
  @Override
  public List<CommonPrefix> listCommonPrefixesObjects(String bucketName, String delimiter) {
    try {
      ListObjectsV2Request request =
          ListObjectsV2Request.builder().bucket(bucketName).delimiter(delimiter).build();

      ListObjectsV2Response response = s3Client.listObjectsV2(request);
      return response.commonPrefixes();
    } catch (Exception e) {
      String errorMessage = "Error listing common prefixes in bucket: " + bucketName;
      log.error(errorMessage, e);
      throw new S3CommonPrefixesListingException(errorMessage, e);
    }
  }

  /**
   * List objects in a bucket with a specific prefix.
   *
   * @param bucketName The name of the S3 bucket.
   * @param prefix The prefix to filter objects.
   * @return A ListObjectsV2Response containing the objects.
   * @throws S3Exception if an error occurs while listing objects.
   */
  @Override
  public ListObjectsV2Response listObjects(String bucketName, String prefix) {
    try {
      ListObjectsV2Request request =
          ListObjectsV2Request.builder().bucket(bucketName).prefix(prefix).build();

      return s3Client.listObjectsV2(request);
    } catch (software.amazon.awssdk.services.s3.model.S3Exception e) {
      String errorMessage =
          "Error listing objects with prefix '" + prefix + "' in bucket: " + bucketName;
      log.error(errorMessage, e);
      throw new S3Exception(errorMessage, e);
    }
  }

  /**
   * Get an object from S3.
   *
   * @param bucketName The name of the S3 bucket.
   * @param key The key of the object to retrieve.
   * @return An InputStream for the object.
   * @throws S3Exception if an error occurs while getting the object.
   */
  @Override
  public InputStream getObject(String bucketName, String key) {
    try {
      GetObjectRequest request = GetObjectRequest.builder().bucket(bucketName).key(key).build();
      return new ByteArrayInputStream(s3Client.getObject(request).readAllBytes());
    } catch (software.amazon.awssdk.services.s3.model.S3Exception e) {
      String errorMessage =
          "Error getting object with key '" + key + "' from bucket: " + bucketName;
      log.error(errorMessage, e);
      throw new S3Exception(errorMessage, e);
    } catch (Exception e) {
      String errorMessage =
          "Error reading object data for key '" + key + "' from bucket: " + bucketName;
      log.error(errorMessage, e);
      throw new S3Exception(errorMessage, e);
    }
  }
}
