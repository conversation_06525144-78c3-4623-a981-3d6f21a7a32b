package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import java.io.IOException;
import java.math.BigInteger;
import java.util.List;
import java.util.concurrent.BlockingQueue;

/** Interface for Ethereum event log data access */
public interface EventLogRepository {
  /**
   * Subscribe to all new events
   *
   * @return Queue of transactions containing events
   */
  BlockingQueue<Transaction> subscribe();

  /**
   * Get transactions from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return List of transactions containing events
   */
  List<Transaction> getFilterLogs(BigInteger startBlock, BigInteger endBlock);

  /**
   * Get the current block number
   *
   * @return Current block number
   */
  BigInteger getCurrentBlockNumber() throws IOException;
}
