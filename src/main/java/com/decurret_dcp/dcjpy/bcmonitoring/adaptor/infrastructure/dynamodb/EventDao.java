package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.DataAccessException;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;
import software.amazon.awssdk.services.dynamodb.model.PutItemRequest;

@Component
@Log4j2
public class EventDao implements EventRepository {
  private final PooledDynamoDbService pooledDynamoDbService;
  private final BcmonitoringConfigurationProperties properties;

  public EventDao(
      PooledDynamoDbService pooledDynamoDbService, BcmonitoringConfigurationProperties properties) {
    this.pooledDynamoDbService = pooledDynamoDbService;
    this.properties = properties;
  }

  /**
   * Saves an event to the DynamoDB table using connection pool.
   *
   * @param event The event to save.
   * @return true if the operation was successful, false otherwise.
   * @throws DataAccessException if there was an error during the operation.
   */
  @Override
  public boolean save(@NotNull Event event) {
    BcmonitoringConfigurationProperties.Aws.Dynamodb dynamodb = properties.getAws().getDynamodb();
    try {
      // Use pooled connection service (similar to Go's newConnection/releaseConnection pattern)
      pooledDynamoDbService.executeWithConnection(
          client -> {
            client.putItem(
                PutItemRequest.builder()
                    .tableName(dynamodb.getTableNameWithPrefix(dynamodb.getEventsTableName()))
                    .item(event.toAttributeMap())
                    .build());
            return null;
          });
      return true;
    } catch (DynamoDbException dynamoDbException) {
      String errorMessage =
          "Failed to save event: " + event.transactionHash + ", logIndex: " + event.logIndex;
      log.error(errorMessage, dynamoDbException);
      return false;
    } catch (Exception e) {
      String errorMessage =
          "Unexpected error saving event: "
              + event.transactionHash
              + ", logIndex: "
              + event.logIndex;
      log.error(errorMessage, e);
      return false;
    }
  }
}
