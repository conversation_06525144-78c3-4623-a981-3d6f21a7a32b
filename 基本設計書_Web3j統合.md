# 基本設計書_Web3j統合

## 1. Web3jの概要

### 1.1 Web3jとは
Web3jは、Java/Android向けのEthereumブロックチェーン統合ライブラリです。BCMonitoringシステムでは、Ethereumブロックチェーンとの通信、イベント監視、データ取得の中核技術として使用されています。

### 1.2 BCMonitoringでの役割
- **リアルタイム監視**: WebSocket接続による新規ブロック通知の即座受信
- **データ取得**: WebSocket RPC接続によるブロック詳細・トランザクション情報の取得
- **イベント解析**: スマートコントラクトイベントログの解析とデコーディング

## 2. 利用しているWeb3jメソッド一覧

| メソッド | 用途 | 接続方式 | 実装クラス |
|---------|------|---------|-----------|
| `newHeadsNotifications()` | 新規ブロック通知購読 | WebSocket | EthEventLogDao |
| `ethGetBlockByNumber()` | ブロック詳細取得 | WebSocket RPC | EthEventLogDao |
| `ethGetLogs()` | 過去ブロックのイベントログ取得 | WebSocket RPC | EthEventLogDao |
| `Contract.staticExtractEventParameters()` | イベントパラメータ抽出 | - | EthEventLogDao |
| `EventEncoder.encode()` | イベントシグネチャ生成 | - | AbiParser |

### Web3jメソッド使用の全体シーケンス
```mermaid
sequenceDiagram
    participant BesuNode as BS
    participant Web3j as W3J
    participant BCMonitoring as BCM

    Note over BCM: ABI解析フェーズ
    BCM->>BCM: EventEncoder.encode(event)
    Note right of BCM: イベントシグネチャ生成

    Note over BCM: リアルタイム監視フェーズ
    BCM->>W3J: newHeadsNotifications().subscribe()
    Note right of BCM: 新規ブロック通知購読

    loop 新規ブロック処理
        BS->>W3J: 新規ブロック通知
        W3J->>BCM: onNext(NewHeadsNotification)

        BCM->>W3J: ethGetBlockByNumber(blockNumber, true)
        W3J->>BS: WebSocket RPC Request
        BS->>W3J: Block Data
        W3J->>BCM: EthBlock.Block
        Note right of BCM: ブロック詳細取得

        BCM->>BCM: Contract.staticExtractEventParameters()
        Note right of BCM: イベントパラメータ抽出
    end

    Note over BCM: 過去ブロック処理フェーズ
    BCM->>W3J: ethGetLogs(filter)
    W3J->>BS: WebSocket RPC Request
    BS->>W3J: Log Data Array
    W3J->>BCM: List<EthLog>
    Note right of BCM: 過去ブロックのイベントログ取得

    BCM->>BCM: Contract.staticExtractEventParameters()
    Note right of BCM: イベントパラメータ抽出
```

## 3. Web3jライブラリの使用方針

### 3.1 リアルタイム監視: 新規ブロック通知の即座受信

#### 概要
WebSocket接続を使用してEthereumノードから新規ブロック通知をリアルタイムで受信し、含まれるイベントログを即座に処理します。

#### 処理フロー
```mermaid
sequenceDiagram
    participant BesuNode as BS
    participant Web3jSubscription as W3J
    participant Web3jCaller as W3JC
    participant EthEventLogDao as ELD
    participant AbiParser as AP
    participant BlockingQueue as Q
    participant MonitorEventService as MS

    MS->>ELD: subscribeAll()
    ELD->>W3J: newHeadsNotifications().subscribe()

    loop 新規ブロック監視
        BS->>W3J: 新規ブロック通知
        W3J->>ELD: onNext(NewHeadsNotification)
        ELD->>W3JC: ethGetBlockByNumber(blockNumber, true)
        BS->>W3JC: ブロック詳細データ

        loop ブロック内トランザクション処理
            ELD->>W3JC: ethGetTransactionReceipt(txHash)
            BS->>W3JC: トランザクションレシート

            loop レシート内ログ処理
                ELD->>ELD: convertEthLogToEventEntity(log)
                ELD->>AP: getABIEventByLog(log)
                AP->>ELD: Event定義
                ELD->>AP: getContractAbiEventByLog(log)
                AP->>ELD: ContractAbiEvent
                ELD->>ELD: Contract.staticExtractEventParameters()
                ELD->>ELD: JSON形式でシリアライズ
            end
        end

        ELD->>Q: Transaction.put()
    end

    Q->>MS: Transaction取得
    MS->>MS: DynamoDB保存処理
```

#### 実装詳細
- **接続管理**: `Web3jConfig.getWeb3j()`でWebSocket接続を取得
- **購読処理**: `web3j.newHeadsNotifications().subscribe()`でFlowable購読
- **非同期処理**: onNext/onError/onCompleteコールバックで非同期処理

### 3.2 データ取得: ブロック詳細・トランザクション情報の取得

#### 概要
WebSocket RPC接続を使用してブロック詳細、トランザクション情報、イベントログを取得します。リアルタイム監視と過去ブロック処理の両方で使用されます。

#### 処理フロー（過去ブロック処理）
```mermaid
sequenceDiagram
    participant MonitorEventService as MS
    participant EthEventLogDao as ELD
    participant Web3jCaller as W3JC
    participant BesuNode as BS
    participant AbiParser as AP

    MS->>ELD: getPendingTransactions(blockHeight)
    ELD->>W3JC: ethGetLogs(filter)
    BS->>W3JC: ログデータ

    loop 各ログ処理
        ELD->>W3JC: ethGetBlockByNumber(blockNumber, false)
        BS->>W3JC: ブロックタイムスタンプ

        ELD->>ELD: convertEthLogToEventEntity(log)
        ELD->>AP: getABIEventByLog(log)
        AP->>ELD: Event定義
        ELD->>AP: getContractAbiEventByLog(log)
        AP->>ELD: ContractAbiEvent
        ELD->>ELD: Contract.staticExtractEventParameters()
        ELD->>ELD: JSON形式でシリアライズ
    end

    ELD->>MS: List<Transaction>
```

#### 実装詳細
- **接続管理**: `Web3jConfig.getWeb3jCaller()`で専用WebSocket RPC接続を取得
- **ブロック取得**: `ethGetBlockByNumber(blockNumber, true)`でトランザクション詳細込み取得
- **ログ取得**: `ethGetLogs(filter)`で指定範囲のイベントログを取得

### 3.3 イベント解析: スマートコントラクトイベントログの解析

#### 概要
ABI定義に基づいてイベントログをデコードし、構造化されたデータとして抽出します。

#### 処理フロー
```mermaid
sequenceDiagram
    participant EthEventLogDao as ELD
    participant AbiParser as AP
    participant Web3jContract as W3J

    ELD->>AP: getABIEventByLog(ethLog)
    AP->>AP: contractEventStore検索
    AP->>ELD: Event定義
    ELD->>AP: getContractAbiEventByLog(ethLog)
    AP->>ELD: ContractAbiEvent
    ELD->>W3J: Contract.staticExtractEventParameters()
    W3J->>ELD: EventValues
    ELD->>ELD: index2番ed/non-indexed値分離
    ELD->>ELD: JSON形式でシリアライズ
```

#### 実装詳細
- **イベント定義取得**: ABI定義からイベントシグネチャで検索
- **パラメータ抽出**: `Contract.staticExtractEventParameters()`でデコード
- **データ構造化**: indexed/non-indexed値を分離してJSON形式で保存

## 4. 接続アーキテクチャ

### 4.1 接続方式の使い分け

| 接続方式 | 用途 | 特徴 | 実装 |
|---------|------|------|------|
| WebSocket | リアルタイム監視 | 双方向通信、プッシュ通知 | `web3j` (購読専用) |
| WebSocket RPC | データ取得 | 要求応答型、WebSocket上でRPC実行 | `web3jCaller` (API呼び出し専用) |

### 4.2 接続管理設計

#### 接続インスタンス管理
- **キャッシュ機能**: `Web3jConfig`で接続インスタンスをキャッシュ
- **遅延初期化**: 初回アクセス時に接続を作成
- **分離設計**: 購読用とAPI呼び出し用で別インスタンス

#### 接続設定
```java
// WebSocket接続設定
String wsEndpoint = String.format("ws://%s:%s", host, port);
WebSocketService webSocketService = new WebSocketService(wsEndpoint, true);
webSocketService.connect();
Web3j web3j = Web3j.build(webSocketService);
```

## 5. ABI統合とイベント処理

### 5.1 ABI統合アーキテクチャ

#### コンポーネント構成
- **AbiParser**: ABI JSON解析とイベント定義管理
- **ContractEvents**: コントラクト別イベント定義格納
- **ContractAbiEvent**: 個別イベント定義とメタデータ
- **contractEventStore**: イベント定義のメモリキャッシュ

#### データ構造
```java
// コントラクトアドレス -> イベント定義マップ
Map<String, ContractEvents> contractEventStore

// イベントシグネチャ -> イベント定義マップ  
Map<String, ContractAbiEvent> events
```

### 5.2 イベントフィルタリング

#### ABI定義による動的フィルタリング

**Web3jのエンコードメソッド（EventEncoder.encode()）によるシグネチャ生成**
```java
// イベント定義からシグネチャ生成
Event event = new Event(eventName, typeReferences);
String signature = EventEncoder.encode(event);
```

**登録済みイベントのみ処理対象**
- ABI定義に存在するイベントのみ処理
- 未知イベントは自動的に除外
- コントラクトアドレスとイベントシグネチャの両方で照合

**未知イベントの除外**
```java
public ContractAbiEvent getContractAbiEventByLog(Log log) throws Exception {
    String eventId = log.getTopics().get(0).toLowerCase();
    String logAddress = log.getAddress().toLowerCase();
    
    Map<String, ContractAbiEvent> events = 
        contractEventStore.containsKey(logAddress) 
            ? contractEventStore.get(logAddress).events 
            : Collections.emptyMap();
            
    return events.containsKey(eventId) ? events.get(eventId) : null;
}
```

### 5.3 イベントデコーディング

#### デコーディング処理フロー
1. **イベント定義取得**: ログのトピック[0]からイベントシグネチャを抽出
2. **ABI照合**: contractEventStoreからイベント定義を検索
3. **パラメータ抽出**: `Contract.staticExtractEventParameters()`でデコード
4. **値分離**: indexed/non-indexed値を分離
5. **JSON変換**: 構造化データをJSON形式でシリアライズ

#### 実装例
```java
// イベント値抽出
EventValues eventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);
List<Type> indexedParameters = eventValues.getIndexedValues();
List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();

// パラメータ名とのマッピング
Map<Boolean, List<AbiEventInput>> groupedInputs = 
    contractAbiEvent.getInputs().stream()
        .collect(Collectors.groupingBy(AbiEventInput::isIndexed));
```

## 6. エラーハンドリング

### 6.1 WebSocket切断

#### 自動再接続設定
- **エラー検知**: onErrorコールバックで接続エラーを検知
- **リソース解放**: `unsubscribe()` + `shutdownWeb3j()`で適切にクリーンアップ
- **再起動機能**: MonitoringRunnerConfigによる自動再起動

#### 実装詳細
```java
web3j.newHeadsNotifications().subscribe(
    // onNext: 正常処理
    newHeadsNotification -> { /* 処理 */ },
    
    // onError: 接続エラー時
    error -> {
        logger.error("Subscription error", error);
        unsubscribe();
        web3jConfig.shutdownWeb3j();
        // エラートランザクション生成（blockNumber=-1）
    },
    
    // onComplete: 購読完了時
    () -> logger.info("Subscription completed")
);
```

### 6.2 WebSocket RPC エラー

#### エラー分類と対応
- **接続エラー**: Web3jConnectionException発生、例外スロー
- **タイムアウト**: 設定値に基づく自動リトライ
- **データエラー**: ログ出力後、処理継続またはスキップ

### 6.3 データレベルエラー

#### エラーハンドリング戦略
- **ブロック処理エラー**: エラートランザクション生成（blockNumber=-1）
- **イベント解析エラー**: 該当ログをスキップ、処理継続
- **ABI定義不一致**: 未知イベントとして除外

#### エラー通知機能
```java
// エラー状態をキューに通知
transactions.put(
    Transaction.builder()
        .blockHeight(BlockHeight.builder().blockNumber(-1).build())
        .build()
);
```

## 7. パフォーマンス考慮事項

### 7.1 接続プール管理
- WebSocket接続: 1接続（購読専用）
- WebSocket RPC接続: 1接続（API呼び出し専用）
- 接続の使い分けによる負荷分散

### 7.2 メモリ管理
- BlockingQueue: Integer.MAX_VALUE（実質無制限）
- contractEventStore: メモリキャッシュによる高速アクセス
- イベントデータ: JSON形式でコンパクトに保存

### 7.3 処理効率
- 非同期処理: Flowableによる非ブロッキング処理
- バッチ処理: 過去ブロック処理での効率的なログ取得
- フィルタリング: ABI定義による事前フィルタリングで不要な処理を削減
